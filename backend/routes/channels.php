<?php

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Private user channel for notifications (toasts, UI updates, etc.)
Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Public chat channel - anyone can listen
Broadcast::channel('chat', function () {
    return true; // Allow anyone to listen
});

// Alternative private user channel name for easier reference
Broadcast::channel('user.{id}', function ($user, $id) {
    Log::info('Channel auth for user.' . $id, [
        'authenticated_user' => $user ? $user->id : 'none',
        'requested_id' => $id,
        'match' => $user && (int) $user->id === (int) $id
    ]);
    return (int) $user->id === (int) $id;
});
