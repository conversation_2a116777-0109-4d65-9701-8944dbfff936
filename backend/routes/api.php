<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\MessageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

// Register broadcasting routes with Sanctum auth
Broadcast::routes(['middleware' => ['auth:sanctum']]);

// Message routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/messages', [MessageController::class, 'send']);
    Route::get('/messages/history', [MessageController::class, 'history']);
});

// Profile routes
Route::middleware('auth:sanctum')->group(function () {
    Route::put('/profile', [ProfileController::class, 'updateProfile']);
    Route::put('/profile/password', [ProfileController::class, 'updatePassword']);
    Route::post('/profile/image', [ProfileController::class, 'updateProfileImage']);
    Route::delete('/profile/image', [ProfileController::class, 'deleteProfileImage']);
});
