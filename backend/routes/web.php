<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;
use App\Events\ToastNotification;
use App\Models\User;

Route::get('/', function () {
    return view('welcome');
});

// Test route for toast notifications (remove in production)
Route::get('/test-toast/{userId}', function ($userId) {
    $user = User::find($userId);
    if (!$user) {
        return response()->json(['error' => 'User not found'], 404);
    }

    Log::info('Dispatching toast notification to user ' . $userId);
    ToastNotification::dispatch($user, 'success', 'Test Notification', 'This is a test toast notification from WebSocket!');

    return response()->json(['message' => 'Toast notification sent to user ' . $userId]);
})->middleware('auth:sanctum');

// Simple test route without auth for debugging
Route::get('/test-broadcast', function () {
    Log::info('Broadcasting test message to chat channel');
    broadcast(new \App\Events\MessageSent(\App\Models\Message::create([
        'user_id' => 1,
        'content' => 'Test broadcast message'
    ])));

    return response()->json(['message' => 'Test broadcast sent']);
});
