<?php

use Illuminate\Support\Facades\Route;
use App\Events\ToastNotification;
use App\Models\User;

Route::get('/', function () {
    return view('welcome');
});

// Test route for toast notifications (remove in production)
Route::get('/test-toast/{userId}', function ($userId) {
    $user = User::find($userId);
    if (!$user) {
        return response()->json(['error' => 'User not found'], 404);
    }

    ToastNotification::dispatch($user, 'success', 'Test Notification', 'This is a test toast notification!');

    return response()->json(['message' => 'Toast notification sent to user ' . $userId]);
})->middleware('auth:sanctum');
