import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

// Define window with Pusher property
declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

// Make Pusher available globally
window.Pusher = Pusher;

// Initialize Laravel Echo with explicit port configuration
const echo = new Echo({
  broadcaster: 'pusher',
  key: import.meta.env.VITE_REVERB_APP_KEY || 'myappkey',
  // Explicitly set the WebSocket host and port
  wsHost: import.meta.env.VITE_REVERB_HOST || 'localhost',
  wsPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  wssPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  // Disable automatic TLS unless explicitly configured
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME || 'http') === 'https',
  // Disable cluster to prevent <PERSON><PERSON><PERSON> from trying to connect to Pusher.com
  cluster: '',
  // Explicitly disable encrypted connections for local development
  encrypted: false,
  // Disable stats to prevent <PERSON><PERSON><PERSON> from sending usage data
  disableStats: true,
  // Only enable WebSocket transports
  enabledTransports: ['ws'], // add wss for secure
  // Add auth endpoint for private channels
  authEndpoint: `${import.meta.env.VITE_API_URL}/broadcasting/auth`,
  authorizer: (channel: any, options: any) => {
    return {
      authorize: (socketId: string, callback: Function) => {
        const token = localStorage.getItem('auth_token')
        if (!token) {
          callback(true, null)
          return
        }

        fetch(`${import.meta.env.VITE_API_URL}/broadcasting/auth`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
          },
          body: JSON.stringify({
            socket_id: socketId,
            channel_name: channel.name
          })
        })
        .then(response => response.json())
        .then(data => {
          callback(false, data)
        })
        .catch(error => {
          console.error('Auth error:', error)
          callback(true, null)
        })
      }
    }
  }
});

// Add debugging to see if the token is being sent correctly
console.log('Auth token:', localStorage.getItem('auth_token'));

// Add connection debugging
if (echo.connector.pusher) {
  echo.connector.pusher.connection.bind('connected', () => {
    console.log('Connected to Reverb WebSocket server!');
    console.log('Socket ID:', echo.socketId());
  });

  echo.connector.pusher.connection.bind('error', (err: any) => {
    console.error('Pusher connection error:', err);
  });
}

export default echo;
