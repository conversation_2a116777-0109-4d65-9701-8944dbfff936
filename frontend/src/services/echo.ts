import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

// Define window with Pusher property
declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

// Make Pusher available globally
window.Pusher = Pusher;

// Initialize Laravel Echo with Reverb configuration
const echo = new Echo({
  broadcaster: 'reverb',
  key: import.meta.env.VITE_REVERB_APP_KEY || 'app-key',
  // Explicitly set the WebSocket host and port
  wsHost: import.meta.env.VITE_REVERB_HOST || 'localhost',
  wsPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  wssPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  // Disable automatic TLS unless explicitly configured
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME || 'http') === 'https',
  // Only enable WebSocket transports
  enabledTransports: ['ws'], // add wss for secure
  // Add auth endpoint for private channels
  authEndpoint: `${import.meta.env.VITE_API_URL}/broadcasting/auth`,
  auth: {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
      Accept: 'application/json',
      'Content-Type': 'application/json'
    }
  },
  // Add authorizer for debugging
  authorizer: (channel: any, options: any) => {
    return {
      authorize: (socketId: string, callback: Function) => {
        const token = localStorage.getItem('auth_token')
        console.log('Echo Service: Authorizing channel:', channel.name, 'with token:', !!token)

        if (!token) {
          console.error('Echo Service: No auth token available for private channel')
          callback(true, null)
          return
        }

        fetch(`${import.meta.env.VITE_API_URL}/broadcasting/auth`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            socket_id: socketId,
            channel_name: channel.name
          })
        })
        .then(response => {
          console.log('Echo Service: Auth response status:', response.status)
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`)
          }
          return response.json()
        })
        .then(data => {
          console.log('Echo Service: Auth successful:', data)
          callback(false, data)
        })
        .catch(error => {
          console.error('Echo Service: Auth failed:', error)
          callback(true, null)
        })
      }
    }
  }
});

// Add debugging to see if the token is being sent correctly
console.log('Echo Service: Auth token available:', !!localStorage.getItem('auth_token'));
console.log('Echo Service: API URL:', import.meta.env.VITE_API_URL);
console.log('Echo Service: Reverb config:', {
  key: import.meta.env.VITE_REVERB_APP_KEY,
  host: import.meta.env.VITE_REVERB_HOST,
  port: import.meta.env.VITE_REVERB_PORT,
  scheme: import.meta.env.VITE_REVERB_SCHEME
});

// Add connection debugging
if (echo.connector.pusher) {
  echo.connector.pusher.connection.bind('connected', () => {
    console.log('Echo Service: Connected to Reverb WebSocket server!');
    console.log('Echo Service: Socket ID:', echo.socketId());
  });

  echo.connector.pusher.connection.bind('disconnected', () => {
    console.log('Echo Service: Disconnected from Reverb WebSocket server');
  });

  echo.connector.pusher.connection.bind('error', (err: any) => {
    console.error('Echo Service: Connection error:', err);
  });

  echo.connector.pusher.connection.bind('state_change', (states: any) => {
    console.log('Echo Service: Connection state changed:', states);
  });
}

export default echo;
