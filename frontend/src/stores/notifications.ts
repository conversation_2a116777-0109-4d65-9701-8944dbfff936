import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useAuthStore } from './auth'
import { useToastNotifications } from '@/composables/useToastNotifications'

export const useNotificationsStore = defineStore('notifications', () => {
  const authStore = useAuthStore()
  const { isListening, startListening, stopListening, reconnect } = useToastNotifications()

  const isInitialized = ref(false)

  // Initialize notifications when user is authenticated
  const initialize = () => {
    if (authStore.isAuthenticated && authStore.user && authStore.token && !isInitialized.value) {
      console.log('Notifications Store: Initializing toast notifications for user', authStore.user.id)
      // Small delay to ensure Echo is fully connected
      setTimeout(() => {
        startListening()
        isInitialized.value = true
      }, 1000)
    } else {
      console.log('Notifications Store: Cannot initialize - auth:', authStore.isAuthenticated, 'user:', !!authStore.user, 'token:', !!authStore.token, 'initialized:', isInitialized.value)
    }
  }

  // Cleanup notifications when user logs out
  const cleanup = () => {
    if (isInitialized.value) {
      console.log('Notifications Store: Cleaning up toast notifications')
      stopListening()
      isInitialized.value = false
    }
  }

  // Watch for authentication changes
  watch(() => authStore.isAuthenticated, (isAuth) => {
    if (isAuth) {
      initialize()
    } else {
      cleanup()
    }
  }, { immediate: true })

  // Watch for user changes (in case user data loads after auth)
  watch(() => authStore.user, (user) => {
    if (user && authStore.isAuthenticated && !isInitialized.value) {
      initialize()
    }
  }, { immediate: true })

  return {
    isListening,
    isInitialized,
    initialize,
    cleanup,
    reconnect
  }
})
