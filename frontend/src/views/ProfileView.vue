<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import ImageCropper from '@/components/ImageCropper.vue'
import axios from 'axios'

const authStore = useAuthStore()

// Profile form data
const username = ref('')
const isUpdatingProfile = ref(false)

// Password form data
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const isUpdatingPassword = ref(false)

// Profile image data
const profileImageFile = ref<File | null>(null)
const profileImagePreview = ref('')
const isUpdatingImage = ref(false)
const fileInput = ref<HTMLInputElement>()

// Image cropping data
const showCropModal = ref(false)

onMounted(() => {
  if (authStore.user) {
    username.value = authStore.user.username || ''
  }
})

// Profile update functions
const updateProfile = async () => {
  if (!username.value.trim()) {
    return
  }

  isUpdatingProfile.value = true

  try {
    const response = await axios.put(`${import.meta.env.VITE_API_URL}/api/profile`, {
      username: username.value
    }, {
      headers: {
        Authorization: `Bearer ${authStore.token}`
      }
    })

    authStore.user = response.data.user
  } catch (error: any) {
    console.error('Profile update failed:', error)
    // Toast notification will be handled by WebSocket broadcast
  } finally {
    isUpdatingProfile.value = false
  }
}

// Password update functions
const updatePassword = async () => {
  if (!currentPassword.value || !newPassword.value || !confirmPassword.value) {
    return
  }

  if (newPassword.value !== confirmPassword.value) {
    return
  }

  if (newPassword.value.length < 6) {
    return
  }

  isUpdatingPassword.value = true

  try {
    await axios.put(`${import.meta.env.VITE_API_URL}/api/profile/password`, {
      current_password: currentPassword.value,
      new_password: newPassword.value,
      new_password_confirmation: confirmPassword.value
    }, {
      headers: {
        Authorization: `Bearer ${authStore.token}`
      }
    })

    // Clear form on success
    currentPassword.value = ''
    newPassword.value = ''
    confirmPassword.value = ''
  } catch (error: any) {
    console.error('Password update failed:', error)
    // Toast notification will be handled by WebSocket broadcast
  } finally {
    isUpdatingPassword.value = false
  }
}

// Image upload functions
const selectImage = () => {
  fileInput.value?.click()
}

const handleImageSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    if (file.size > 2 * 1024 * 1024) { // 2MB limit
      imageError.value = 'Image must be less than 2MB'
      return
    }

    if (!file.type.startsWith('image/')) {
      imageError.value = 'Please select a valid image file'
      return
    }

    profileImageFile.value = file
    const reader = new FileReader()
    reader.onload = (e) => {
      profileImagePreview.value = e.target?.result as string
      showCropModal.value = true
    }
    reader.readAsDataURL(file)
  }
}

const uploadImage = async (croppedFile: File) => {
  isUpdatingImage.value = true

  try {
    const formData = new FormData()
    formData.append('profile_image', croppedFile)

    const response = await axios.post(`${import.meta.env.VITE_API_URL}/api/profile/image`, formData, {
      headers: {
        Authorization: `Bearer ${authStore.token}`,
        'Content-Type': 'multipart/form-data'
      }
    })

    authStore.user = response.data.user
    profileImageFile.value = null
    profileImagePreview.value = ''
    showCropModal.value = false
  } catch (error: any) {
    console.error('Profile image upload failed:', error)
    // Toast notification will be handled by WebSocket broadcast
  } finally {
    isUpdatingImage.value = false
  }
}

const deleteImage = async () => {
  if (!authStore.user?.profile_image) return

  if (!confirm('Are you sure you want to delete your profile image?')) {
    return
  }

  isUpdatingImage.value = true

  try {
    const response = await axios.delete(`${import.meta.env.VITE_API_URL}/api/profile/image`, {
      headers: {
        Authorization: `Bearer ${authStore.token}`
      }
    })

    authStore.user = response.data.user
  } catch (error: any) {
    console.error('Profile image deletion failed:', error)
    // Toast notification will be handled by WebSocket broadcast
  } finally {
    isUpdatingImage.value = false
  }
}

const cancelCrop = () => {
  showCropModal.value = false
  profileImageFile.value = null
  profileImagePreview.value = ''
}
</script>

<template>
  <div class="profile">
    <h1>Profile Settings</h1>

    <div class="profile-sections">
      <!-- Profile Image Section -->
      <div class="profile-section">
        <h2>Profile Image</h2>
        <div class="image-section">
          <div class="current-image">
            <div class="image-circle">
              <img
                v-if="authStore.user?.profile_image_url"
                :src="authStore.user.profile_image_url"
                alt="Profile"
                class="profile-img"
              />
              <div v-else class="placeholder">
                {{ authStore.user?.username?.charAt(0).toUpperCase() }}
              </div>
            </div>
          </div>

          <div class="image-actions">
            <button @click="selectImage" class="btn btn-primary">
              {{ authStore.user?.profile_image ? 'Change Image' : 'Upload Image' }}
            </button>
            <button
              v-if="authStore.user?.profile_image"
              @click="deleteImage"
              class="btn btn-danger"
            >
              Delete Image
            </button>
          </div>

          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            @change="handleImageSelect"
            style="display: none"
          />


        </div>
      </div>

      <!-- Username Section -->
      <div class="profile-section">
        <h2>Username</h2>
        <form @submit.prevent="updateProfile" class="form">
          <div class="form-group">
            <label for="username">Username</label>
            <input
              id="username"
              v-model="username"
              type="text"
              class="form-input"
              :disabled="isUpdatingProfile"
              required
            />
          </div>

          <button
            type="submit"
            class="btn btn-primary"
            :disabled="isUpdatingProfile"
          >
            {{ isUpdatingProfile ? 'Updating...' : 'Update Username' }}
          </button>


        </form>
      </div>

      <!-- Password Section -->
      <div class="profile-section">
        <h2>Change Password</h2>
        <form @submit.prevent="updatePassword" class="form">
          <div class="form-group">
            <label for="current-password">Current Password</label>
            <input
              id="current-password"
              v-model="currentPassword"
              type="password"
              class="form-input"
              :disabled="isUpdatingPassword"
              required
            />
          </div>

          <div class="form-group">
            <label for="new-password">New Password</label>
            <input
              id="new-password"
              v-model="newPassword"
              type="password"
              class="form-input"
              :disabled="isUpdatingPassword"
              required
            />
          </div>

          <div class="form-group">
            <label for="confirm-password">Confirm New Password</label>
            <input
              id="confirm-password"
              v-model="confirmPassword"
              type="password"
              class="form-input"
              :disabled="isUpdatingPassword"
              required
            />
          </div>

          <button
            type="submit"
            class="btn btn-primary"
            :disabled="isUpdatingPassword"
          >
            {{ isUpdatingPassword ? 'Updating...' : 'Update Password' }}
          </button>


        </form>
      </div>
    </div>

    <!-- Image Crop Modal -->
    <div v-if="showCropModal" class="modal-overlay" @click="cancelCrop">
      <div class="modal" @click.stop>
        <h3>Crop Your Profile Image</h3>
        <ImageCropper
          :image-url="profileImagePreview"
          :crop-size="200"
          @crop="uploadImage"
          @cancel="cancelCrop"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  padding-top: 1rem; /* Reduced since content already has header padding */
}

.profile h1 {
  margin-bottom: 2rem;
  color: #333;
}

.profile-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-section h2 {
  margin-bottom: 1rem;
  color: #555;
  font-size: 1.2rem;
}

/* Image Section */
.image-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.current-image {
  margin-bottom: 1rem;
}

.image-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.profile-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder {
  font-size: 2rem;
  font-weight: bold;
  color: #888;
}

.image-actions {
  display: flex;
  gap: 0.5rem;
}

/* Form Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #555;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #42b883;
}

.form-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.btn-primary {
  background-color: #42b883;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #369870;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}



/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal h3 {
  margin-bottom: 1rem;
  text-align: center;
}



/* Responsive */
@media (max-width: 768px) {
  .profile {
    padding: 1rem;
  }

  .image-actions {
    flex-direction: column;
    align-items: center;
  }

  .modal {
    padding: 1rem;
  }
}
</style>
