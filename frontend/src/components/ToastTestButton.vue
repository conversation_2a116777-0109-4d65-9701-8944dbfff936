<template>
  <div class="toast-test">
    <h3>Toast Test Controls</h3>
    <div class="test-buttons">
      <button @click="testSuccess" class="btn btn-success">Test Success Toast</button>
      <button @click="testError" class="btn btn-error">Test Error Toast</button>
      <button @click="testInfo" class="btn btn-info">Test Info Toast</button>
      <button @click="testWarning" class="btn btn-warning">Test Warning Toast</button>
      <button @click="testWebSocketToast" class="btn btn-primary" :disabled="!authStore.user">
        Test WebSocket Toast
      </button>
      <button @click="reconnectNotifications" class="btn btn-secondary">
        Reconnect Notifications
      </button>
    </div>
    <div class="connection-status">
      <p>Auth: {{ authStore.isAuthenticated ? '✅' : '❌' }}</p>
      <p>User ID: {{ authStore.user?.id || 'None' }}</p>
      <p>Toast Listening: {{ notificationsStore.isListening ? '✅' : '❌' }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { useNotificationsStore } from '@/stores/notifications'
import toastService from '@/services/toast'
import axios from 'axios'

const authStore = useAuthStore()
const notificationsStore = useNotificationsStore()

const testSuccess = () => {
  toastService.success('Success Test', 'This is a test success message')
}

const testError = () => {
  toastService.error('Error Test', 'This is a test error message')
}

const testInfo = () => {
  toastService.info('Info Test', 'This is a test info message')
}

const testWarning = () => {
  toastService.warning('Warning Test', 'This is a test warning message')
}

const testWebSocketToast = async () => {
  if (!authStore.user) return

  try {
    const response = await axios.get(`${import.meta.env.VITE_API_URL}/test-toast/${authStore.user.id}`, {
      headers: {
        Authorization: `Bearer ${authStore.token}`
      }
    })
    console.log('WebSocket toast test response:', response.data)
  } catch (error) {
    console.error('WebSocket toast test failed:', error)
    toastService.error('Test Failed', 'Could not send WebSocket toast')
  }
}

const reconnectNotifications = () => {
  console.log('Manually reconnecting notifications...')
  notificationsStore.cleanup()
  setTimeout(() => {
    notificationsStore.initialize()
  }, 1000)
}
</script>

<style scoped>
.toast-test {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.test-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-success { background: #28a745; color: white; }
.btn-error { background: #dc3545; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-primary { background: #007bff; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.btn:disabled { opacity: 0.5; cursor: not-allowed; }

.connection-status {
  font-size: 0.9rem;
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
}

.connection-status p {
  margin: 0.25rem 0;
}
</style>
